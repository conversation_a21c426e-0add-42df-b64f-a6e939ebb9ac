"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupQueue = exports.getQueueStatus = exports.addToProcessingQueue = exports.processQueue = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
/**
 * Process items in the processing queue
 */
exports.processQueue = functions.https.onCall(async (data, context) => {
    // Check authentication
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        // Get pending queue items
        const queueSnapshot = await admin
            .firestore()
            .collection("processing_queue")
            .where("status", "==", "pending")
            .orderBy("timestamp")
            .limit(10) // Process in batches
            .get();
        const processedItems = [];
        const failedItems = [];
        for (const doc of queueSnapshot.docs) {
            try {
                const queueItem = doc.data();
                await processQueueItem(doc.id, queueItem);
                processedItems.push(doc.id);
            }
            catch (error) {
                console.error(`Failed to process queue item ${doc.id}:`, error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                failedItems.push({ id: doc.id, error: errorMessage });
                // Update item status to failed
                await doc.ref.update({
                    status: "failed",
                    errorMessage: errorMessage,
                    processedAt: admin.firestore.FieldValue.serverTimestamp(),
                });
            }
        }
        return {
            success: true,
            processedCount: processedItems.length,
            failedCount: failedItems.length,
            processedItems,
            failedItems,
        };
    }
    catch (error) {
        console.error("Error processing queue:", error);
        throw new functions.https.HttpsError("internal", `Failed to process queue: ${error}`);
    }
});
/**
 * Add item to processing queue
 */
exports.addToProcessingQueue = functions.https.onCall(async (data, context) => {
    // Check authentication
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    const { documentId, type = "metadata_generation" } = data;
    if (!documentId) {
        throw new functions.https.HttpsError("invalid-argument", "Document ID is required");
    }
    try {
        // Get document details
        const documentDoc = await admin
            .firestore()
            .collection("documents")
            .doc(documentId)
            .get();
        if (!documentDoc.exists) {
            throw new functions.https.HttpsError("not-found", "Document not found");
        }
        const documentData = documentDoc.data();
        // Create queue item
        const queueItem = {
            type,
            filePath: documentData === null || documentData === void 0 ? void 0 : documentData.filePath,
            contentType: documentData === null || documentData === void 0 ? void 0 : documentData.fileType,
            metadata: {
                documentId,
                fileName: documentData === null || documentData === void 0 ? void 0 : documentData.fileName,
                fileSize: documentData === null || documentData === void 0 ? void 0 : documentData.fileSize,
                uploadedBy: documentData === null || documentData === void 0 ? void 0 : documentData.uploadedBy,
            },
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            status: "pending",
            retryCount: 0,
        };
        // Add to queue
        const queueRef = await admin
            .firestore()
            .collection("processing_queue")
            .add(queueItem);
        // Log activity
        await admin
            .firestore()
            .collection("activities")
            .add({
            type: "queue_add",
            documentId,
            userId: context.auth.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: `Document added to processing queue: ${type}`,
        });
        return {
            success: true,
            queueItemId: queueRef.id,
            message: "Document added to processing queue",
        };
    }
    catch (error) {
        console.error("Error adding to processing queue:", error);
        throw new functions.https.HttpsError("internal", `Failed to add to processing queue: ${error}`);
    }
});
/**
 * Get processing queue status
 */
exports.getQueueStatus = functions.https.onCall(async (data, context) => {
    // Check authentication
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const queueSnapshot = await admin
            .firestore()
            .collection("processing_queue")
            .get();
        const statusCounts = {
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            permanently_failed: 0,
        };
        queueSnapshot.docs.forEach((doc) => {
            const status = doc.data().status || "pending";
            if (status in statusCounts) {
                statusCounts[status]++;
            }
        });
        return {
            success: true,
            statusCounts,
            totalItems: queueSnapshot.size,
        };
    }
    catch (error) {
        console.error("Error getting queue status:", error);
        throw new functions.https.HttpsError("internal", `Failed to get queue status: ${error}`);
    }
});
/**
 * Clean up completed queue items
 */
exports.cleanupQueue = functions.https.onCall(async (data, context) => {
    // Check authentication and admin status
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    const { olderThanDays = 7 } = data;
    try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        // Get completed items older than cutoff date
        const completedSnapshot = await admin
            .firestore()
            .collection("processing_queue")
            .where("status", "==", "completed")
            .where("processedAt", "<", admin.firestore.Timestamp.fromDate(cutoffDate))
            .get();
        const batch = admin.firestore().batch();
        let deletedCount = 0;
        completedSnapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
            deletedCount++;
        });
        if (deletedCount > 0) {
            await batch.commit();
        }
        return {
            success: true,
            deletedCount,
            message: `Cleaned up ${deletedCount} completed queue items`,
        };
    }
    catch (error) {
        console.error("Error cleaning up queue:", error);
        throw new functions.https.HttpsError("internal", `Failed to cleanup queue: ${error}`);
    }
});
/**
 * Process a single queue item
 */
async function processQueueItem(itemId, queueItem) {
    // Update status to processing
    await admin.firestore().collection("processing_queue").doc(itemId).update({
        status: "processing",
        processedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
    switch (queueItem.type) {
        case "metadata_generation":
            await processMetadataGeneration(queueItem);
            break;
        case "file_upload":
            await processFileUpload(queueItem);
            break;
        case "file_processing":
            await processFileProcessing(queueItem);
            break;
        default:
            throw new Error(`Unknown queue item type: ${queueItem.type}`);
    }
    // Update status to completed
    await admin.firestore().collection("processing_queue").doc(itemId).update({
        status: "completed",
        processedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
}
/**
 * Process metadata generation
 */
async function processMetadataGeneration(queueItem) {
    var _a;
    const documentId = (_a = queueItem.metadata) === null || _a === void 0 ? void 0 : _a.documentId;
    if (!documentId) {
        throw new Error("Document ID not found in queue item metadata");
    }
    // Get document
    const documentDoc = await admin
        .firestore()
        .collection("documents")
        .doc(documentId)
        .get();
    if (!documentDoc.exists) {
        throw new Error(`Document not found: ${documentId}`);
    }
    const documentData = documentDoc.data();
    if (!documentData) {
        throw new Error(`Document data not found: ${documentId}`);
    }
    // Generate metadata
    const generatedMetadata = generateMetadata(documentData);
    // Update document with generated metadata
    await admin.firestore().collection("documents").doc(documentId).update({
        "metadata.description": generatedMetadata.description,
        "metadata.tags": generatedMetadata.tags,
        status: "active", // Change from pending to active
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });
}
/**
 * Process file upload
 */
async function processFileUpload(queueItem) {
    // This would typically involve:
    // 1. Validating the uploaded file
    // 2. Generating thumbnails if needed
    // 3. Extracting metadata
    // 4. Creating document record
    console.log("Processing file upload:", queueItem.filePath);
}
/**
 * Process general file processing
 */
async function processFileProcessing(queueItem) {
    // This would handle various file processing tasks
    console.log("Processing file:", queueItem.filePath);
}
/**
 * Generate metadata for a document
 */
function generateMetadata(documentData) {
    var _a, _b;
    const fileName = ((_a = documentData.fileName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || "";
    const fileType = ((_b = documentData.fileType) === null || _b === void 0 ? void 0 : _b.toLowerCase()) || "";
    let description = "";
    const tags = [];
    // Generate description based on file type
    switch (fileType) {
        case "pdf":
            description = `PDF document: ${documentData.fileName}`;
            tags.push("pdf", "document");
            break;
        case "doc":
        case "docx":
            description = `Word document: ${documentData.fileName}`;
            tags.push("word", "document", "text");
            break;
        case "xlsx":
        case "xls":
            description = `Excel spreadsheet: ${documentData.fileName}`;
            tags.push("excel", "spreadsheet", "data");
            break;
        case "ppt":
        case "pptx":
            description = `PowerPoint presentation: ${documentData.fileName}`;
            tags.push("powerpoint", "presentation", "slides");
            break;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
            description = `Image file: ${documentData.fileName}`;
            tags.push("image", "picture");
            break;
        default:
            description = `File: ${documentData.fileName}`;
            tags.push("file");
    }
    // Add tags based on filename patterns
    if (fileName.includes("report"))
        tags.push("report");
    if (fileName.includes("invoice"))
        tags.push("invoice");
    if (fileName.includes("contract"))
        tags.push("contract");
    if (fileName.includes("meeting"))
        tags.push("meeting");
    if (fileName.includes("proposal"))
        tags.push("proposal");
    if (fileName.includes("budget"))
        tags.push("budget");
    if (fileName.includes("plan"))
        tags.push("plan");
    // Add category tag if available
    if (documentData.category && documentData.category !== "uncategorized") {
        tags.push(documentData.category);
    }
    return {
        description,
        tags: [...new Set(tags)], // Remove duplicates
    };
}
//# sourceMappingURL=processingQueue.js.map